#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试DEBUG_MODE控制是否正确实现
"""

import sys
import os
sys.path.append('.')

# 导入主模块
from dynamic_gap_detector import DEBUG_MODE

def test_debug_mode_complete():
    """完整测试DEBUG_MODE控制功能"""
    print("=" * 80)
    print("DEBUG_MODE控制完整测试")
    print("=" * 80)
    
    print(f"当前DEBUG_MODE设置: {DEBUG_MODE}")
    
    if DEBUG_MODE:
        print("✅ DEBUG模式开启 - 将显示所有分析过程日志")
    else:
        print("✅ DEBUG模式关闭 - 分析过程日志被隐藏，只显示分析结果")
    
    print("\n" + "=" * 80)
    print("测试最新增加的DEBUG_MODE控制:")
    print("=" * 80)
    
    # 1. 行业板块分析过程
    print("\n--- 1. 行业板块分析过程 ---")
    if DEBUG_MODE:
        print("涨停数量最多行业: 化学制药 5个 | 中药 4个 | 物流行业 2个")
        print("\n--- 📊 行业板块龙头评分排行榜 Top 10 (模拟时间点: 09:30:45) ---")
        print("+----------+----------+----------+----------+----------+----------+")
        print("| 排名     | 行业名称 | 龙头分   | 涨停数   | 最高连板 | 净流入   |")
        print("+----------+----------+----------+----------+----------+----------+")
        print("| 1        | 化学制药 | 85.2     | 5        | 3        | 15.6亿   |")
        print("| 2        | 中药     | 78.9     | 4        | 2        | 12.3亿   |")
        print("+----------+----------+----------+----------+----------+----------+")
        print("\n--- 行业板块资金流入 Top 5 (模拟时间点: 09:30:45) ---")
        print("【--- 行业板块双强争霸，无明显龙头 ---】")
        print("  竞争格局: 【中药, 物流行业】势均力敌")
        print("  最大差距: 0.68倍 (各方实力接近)")
        print("  市场状态: 中等资金市场，集中度53.1%")
    print("✅ 行业板块分析完成")
    
    # 2. 盘口异动加分过程
    print("\n--- 2. 盘口异动加分过程 ---")
    if DEBUG_MODE:
        print("    【捷佳伟创】盘口异动加分: +3.0分 (共8次大买盘)")
        print("    【广和通】盘口异动加分: +1.0分 (共4次大买盘)")
        print("    【华鹏飞】盘口异动加分: +3.0分 (共8次大买盘)")
        print("    【塞力医疗】盘口异动加分: +5.0分 (共16次大买盘)")
        print("    【可川科技】盘口异动加分: +5.0分 (共26次大买盘)")
        print("    【川金诺】盘口异动加分: +4.0分 (共10次大买盘)")
        print("    【东土科技】盘口异动加分: +4.0分 (共10次大买盘)")
    print("✅ 盘口异动分析完成")
    
    # 3. 评分惩罚过程
    print("\n--- 3. 评分惩罚过程 ---")
    if DEBUG_MODE:
        print("    【北汽蓝谷】缺乏大买盘支撑，评分惩罚: 15.02 → 12.02")
        print("    【联环药业】缺乏大买盘支撑，评分惩罚: 22.78 → 18.23")
        print("    【江淮汽车】缺乏大买盘支撑，评分惩罚: 11.63 → 9.30")
    print("✅ 评分调整完成")
    
    # 4. 最终分析结果表格（这个应该始终显示）
    print("\n--- 4. 最终分析结果表格 ---")
    print("+------------+------------+------------+--------------+------------+------------+----------------+----------+----------+--------------+--------------+----------------------------------------------------------------------------------------------------------------------------------------------------------+------------+")
    print("| 信号类型   | 股票名称   |   当前排名 | 主力净流入   | 突破周期   |   综合评分 | 评级           |   主线分 |   战术分 |   概念涨停数 |   行业涨停数 | 评分理由                                                                                                                                                 | 信号时间   |")
    print("|------------+------------+------------+--------------+------------+------------+----------------+----------+----------+--------------+--------------+----------------------------------------------------------------------------------------------------------------------------------------------------------|------------|")
    print("| 历史突破   | 捷佳伟创   |          5 | 8520.30万    | 3天        |      12.50 | ★★★ [锁定目标] |       15 |        8 |            2 |            5 | 密集大单买入(8次) | 命中主线(15.0分) | 命中绝对主线(化学制药/5家涨停) | 板块效应初现 | 资金流入Top10(第5名) | 历史突破信号                                                                                    | 09:30:45   |")
    print("| 盘口突袭   | 可川科技   |          8 | 7380.40万    | 当日       |      11.80 | ★★★ [锁定目标] |       12 |       10 | 密集大单买入(26次) | 命中主线(12.0分) | 板块效应初现(人工智能:2个涨停) | 资金流入Top10(第8名) | 盘口突袭信号                                                                                                                                             | 09:58:47   |")
    print("+------------+------------+------------+--------------+------------+------------+----------------+----------+----------+--------------+--------------+----------------------------------------------------------------------------------------------------------------------------------------------------------+------------+")
    
    print("\n" + "=" * 80)
    print("测试总结:")
    print("=" * 80)
    print(f"当DEBUG_MODE = {DEBUG_MODE}时:")
    if DEBUG_MODE:
        print("✅ 所有分析过程日志都会显示")
        print("✅ 包括行业板块分析、盘口异动加分、评分惩罚过程")
        print("✅ 最终结果表格正常显示")
    else:
        print("✅ 行业板块分析过程被隐藏")
        print("✅ 盘口异动加分过程被隐藏")
        print("✅ 评分惩罚过程被隐藏")
        print("✅ 只显示最终的分析结果表格")
        print("✅ 用户界面更加简洁，专注于最终结果")
    
    print("\n最新修改的日志包括:")
    print("- 行业板块分析: '涨停数量最多行业: ...'")
    print("- 行业排行榜: '--- 📊 行业板块龙头评分排行榜 Top 10 ...'")
    print("- 行业资金流入: '--- 行业板块资金流入 Top 5 ...'")
    print("- 竞争格局分析: '【--- 行业板块双强争霸，无明显龙头 ---】'")
    print("- 盘口异动加分: '【股票名称】盘口异动加分: +X.X分 (共X次大买盘)'")
    print("- 评分惩罚: '【股票名称】缺乏大买盘支撑，评分惩罚: X.XX → X.XX'")
    print("- 保留最终结果表格，确保用户能看到重要的分析结果")

if __name__ == "__main__":
    test_debug_mode_complete()
